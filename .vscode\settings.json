{"dart.flutterSdkPath": null, "dart.lineLength": 100, "dart.showTodos": true, "dart.closingLabels": true, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80, 100], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "files.associations": {"*.dart": "dart"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/.dart_tool": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/.packages": true, "**/.pub-cache": true, "**/.pub": true, "**/build": true}, "search.exclude": {"**/.dart_tool": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/.packages": true, "**/.pub-cache": true, "**/.pub": true, "**/build": true, "ios/Pods": true, "ios/Runner.xcworkspace": true}}