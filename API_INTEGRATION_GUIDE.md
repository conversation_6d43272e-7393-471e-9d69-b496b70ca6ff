# دليل ربط API أجهزة ZTE الحقيقي

## نظرة عامة

هذا الدليل يوضح كيفية ربط تطبيق ZTELink Flutter مع API الحقيقي لأجهزة ZTE Mi-Fi و CPE.

## بنية API أجهزة ZTE

### 1. المصادقة (Authentication)

```http
GET /api/webserver/SesTokInfo
```

**الاستجابة:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<response>
    <SesInfo>SESSION_TOKEN_HERE</SesInfo>
    <TokInfo>TOKEN_ID_HERE</TokInfo>
</response>
```

### 2. تسجيل الدخول

```http
POST /api/user/login
Content-Type: application/x-www-form-urlencoded
__RequestVerificationToken: SESSION_TOKEN
Cookie: SessionID=TOKEN_ID

<?xml version="1.0" encoding="UTF-8"?>
<request>
    <Username>admin</Username>
    <Password>HASHED_PASSWORD</Password>
    <password_type>4</password_type>
</request>
```

## API endpoints للنطاق الترددي

### 1. الحصول على إعدادات النطاق

```http
GET /api/qos/bandwidth-settings
Authorization: Bearer SESSION_TOKEN
```

**الاستجابة المتوقعة:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<response>
    <UploadSpeed>1000</UploadSpeed>
    <DownloadSpeed>1000</DownloadSpeed>
    <BandwidthEnabled>1</BandwidthEnabled>
    <Unit>kbps</Unit>
</response>
```

### 2. تحديث إعدادات النطاق

```http
POST /api/qos/bandwidth-settings
Content-Type: application/x-www-form-urlencoded
__RequestVerificationToken: SESSION_TOKEN

<?xml version="1.0" encoding="UTF-8"?>
<request>
    <UploadSpeed>2000</UploadSpeed>
    <DownloadSpeed>5000</DownloadSpeed>
    <BandwidthEnabled>1</BandwidthEnabled>
    <Unit>kbps</Unit>
</request>
```

### 3. إحصائيات الترافيك

```http
GET /api/monitoring/traffic-statistics
```

**الاستجابة:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<response>
    <TotalUpload>1048576</TotalUpload>
    <TotalDownload>5242880</TotalDownload>
    <CurrentUploadSpeed>1024</CurrentUploadSpeed>
    <CurrentDownloadSpeed>2048</CurrentDownloadSpeed>
    <MonthlyUpload>104857600</MonthlyUpload>
    <MonthlyDownload>524288000</MonthlyDownload>
    <DailyUpload>10485760</DailyUpload>
    <DailyDownload>52428800</DailyDownload>
    <ConnectionType>4G</ConnectionType>
    <ConnectedDevices>5</ConnectedDevices>
</response>
```

## تكوين التطبيق للعمل مع جهاز حقيقي

### 1. تحديث عنوان IP

```dart
// في lib/constants/app_constants.dart
static const String baseUrl = 'http://***********'; // عنوان جهاز ZTE
```

### 2. تفعيل أذونات الشبكة

**Android (android/app/src/main/AndroidManifest.xml):**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
```

**iOS (ios/Runner/Info.plist):**
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

### 3. اختبار الاتصال

```dart
// اختبار بسيط للاتصال
Future<bool> testConnection() async {
  try {
    final response = await http.get(
      Uri.parse('http://***********/api/webserver/SesTokInfo'),
      headers: {'Accept': 'application/xml'},
    );
    return response.statusCode == 200;
  } catch (e) {
    return false;
  }
}
```

## معالجة الأخطاء الشائعة

### 1. خطأ CORS (للويب)

```dart
// إضافة headers للتعامل مع CORS
final headers = {
  'Content-Type': 'application/x-www-form-urlencoded',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};
```

### 2. خطأ SSL Certificate

```dart
// تجاهل شهادات SSL للاختبار (غير آمن للإنتاج)
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

// في main()
HttpOverrides.global = MyHttpOverrides();
```

### 3. Timeout Issues

```dart
final dio = Dio(BaseOptions(
  connectTimeout: const Duration(seconds: 30),
  receiveTimeout: const Duration(seconds: 30),
  sendTimeout: const Duration(seconds: 30),
));
```

## نصائح للتطوير

### 1. استخدام Mock Data للاختبار

```dart
// في development mode
if (kDebugMode) {
  return _getMockBandwidthSettings();
} else {
  return await _apiService.getBandwidthSettings();
}
```

### 2. Logging مفصل

```dart
// إضافة logging للطلبات
dio.interceptors.add(LogInterceptor(
  requestBody: true,
  responseBody: true,
  requestHeader: true,
  responseHeader: true,
));
```

### 3. Retry Logic

```dart
Future<T?> _retryRequest<T>(Future<T> Function() request, {int maxRetries = 3}) async {
  for (int i = 0; i < maxRetries; i++) {
    try {
      return await request();
    } catch (e) {
      if (i == maxRetries - 1) rethrow;
      await Future.delayed(Duration(seconds: 2 * (i + 1)));
    }
  }
  return null;
}
```

## أمثلة عملية

### 1. تحديث النطاق الترددي

```dart
final provider = Provider.of<BandwidthProvider>(context, listen: false);
final success = await provider.updateSettings(
  uploadSpeed: 2000,    // 2 Mbps
  downloadSpeed: 5000,  // 5 Mbps
  isEnabled: true,
);

if (success) {
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(content: Text('تم تحديث الإعدادات بنجاح')),
  );
}
```

### 2. مراقبة الترافيك في الوقت الفعلي

```dart
final trafficProvider = Provider.of<TrafficProvider>(context);
Timer.periodic(const Duration(seconds: 5), (timer) {
  if (mounted) {
    trafficProvider.refreshData();
  } else {
    timer.cancel();
  }
});
```

## استكشاف الأخطاء

### 1. فحص الاتصال

```bash
# اختبار الاتصال بالجهاز
ping ***********

# اختبار API endpoint
curl -X GET http://***********/api/webserver/SesTokInfo
```

### 2. فحص الاستجابات

```dart
print('Response Status: ${response.statusCode}');
print('Response Headers: ${response.headers}');
print('Response Body: ${response.data}');
```

### 3. مراقبة الشبكة

استخدم أدوات مثل:
- Chrome DevTools (للويب)
- Charles Proxy
- Wireshark

## الأمان

### 1. تشفير كلمات المرور

```dart
String hashPassword(String password, String token) {
  final combined = password + token;
  final bytes = utf8.encode(combined);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
```

### 2. تخزين آمن للبيانات الحساسة

```dart
// استخدام flutter_secure_storage للبيانات الحساسة
const storage = FlutterSecureStorage();
await storage.write(key: 'password', value: hashedPassword);
```

### 3. التحقق من الشهادات

```dart
// للإنتاج، تحقق من شهادات SSL
dio.interceptors.add(CertificatePinningInterceptor(
  allowedSHAFingerprints: ['SHA_FINGERPRINT_HERE'],
));
```

## الخلاصة

هذا الدليل يوفر الأساس لربط التطبيق مع API أجهزة ZTE الحقيقي. تأكد من:

1. ✅ اختبار الاتصال مع الجهاز أولاً
2. ✅ معالجة الأخطاء بشكل صحيح
3. ✅ استخدام HTTPS في الإنتاج
4. ✅ تشفير البيانات الحساسة
5. ✅ إضافة retry logic للطلبات
6. ✅ مراقبة الأداء والاستجابة

للمساعدة الإضافية، راجع وثائق API الخاصة بجهاز ZTE المحدد أو اتصل بالدعم التقني.
