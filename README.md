# ZTELink Flutter

تطبيق Flutter مشابه لـ ZTELink الأصلي لإدارة أجهزة ZTE Mi-Fi و CPE.

## الوصف

ZTELink Flutter هو تطبيق محمول مطور بـ Flutter يحاكي وظائف تطبيق ZTELink الأصلي. يوفر التطبيق واجهة سهلة الاستخدام لإدارة أجهزة ZTE Mi-Fi و CPE، بما في ذلك إدارة الشبكة، مراقبة الأجهزة المتصلة، مشاركة الملفات، والمراسلة.

## الميزات الأساسية

### ✅ المكتملة
- **شاشة البداية (Splash Screen)**: شاشة ترحيب مع شعار التطبيق
- **تسجيل الدخول**: نظام مصادقة مع حفظ بيانات الاعتماد
- **الشاشة الرئيسية**: واجهة رئيسية مع تبويبات متعددة
- **إدارة الحالة**: استخدام Provider لإدارة حالة التطبيق
- **التخزين المحلي**: حفظ الإعدادات وبيانات المستخدم
- **خدمات الشبكة**: API service للتواصل مع أجهزة ZTE

### 🚧 قيد التطوير
- **إدارة WiFi والهوت سبوت**: تكوين إعدادات الشبكة
- **الأجهزة المتصلة**: عرض وإدارة الأجهزة المتصلة
- **نظام المراسلة**: إرسال واستقبال الرسائل
- **مشاركة الملفات**: رفع وتحميل الملفات
- **مراقبة البيانات**: إحصائيات استهلاك البيانات
- **إعدادات الجهاز**: تكوين إعدادات الجهاز المتقدمة

## هيكل المشروع

```
lib/
├── constants/          # الثوابت والقيم الثابتة
│   ├── app_colors.dart
│   ├── app_constants.dart
│   └── app_strings.dart
├── models/            # نماذج البيانات
│   ├── device_info.dart
│   ├── connected_device.dart
│   └── wifi_settings.dart
├── providers/         # مقدمي الحالة (State Management)
│   └── auth_provider.dart
├── screens/          # شاشات التطبيق
│   ├── splash_screen.dart
│   ├── login_screen.dart
│   └── home_screen.dart
├── services/         # خدمات التطبيق
│   ├── api_service.dart
│   └── storage_service.dart
├── widgets/          # الويدجت المخصصة
│   ├── custom_button.dart
│   └── custom_text_field.dart
└── main.dart         # نقطة البداية
```

## المتطلبات

- Flutter SDK (3.0.0 أو أحدث)
- Dart SDK (2.17.0 أو أحدث)
- Android Studio أو VS Code
- جهاز ZTE Mi-Fi أو CPE للاختبار

## التثبيت والتشغيل

1. **استنساخ المشروع**:
   ```bash
   git clone <repository-url>
   cd zte_link_flutter
   ```

2. **تثبيت التبعيات**:
   ```bash
   flutter pub get
   ```

3. **تشغيل التطبيق**:
   ```bash
   # للتشغيل على Android
   flutter run

   # للتشغيل على الويب
   flutter run -d chrome

   # للتشغيل على iOS
   flutter run -d ios
   ```

## الاستخدام

### تسجيل الدخول
1. أدخل عنوان IP الخاص بجهاز ZTE (مثل: ***********)
2. أدخل اسم المستخدم (افتراضي: admin)
3. أدخل كلمة المرور
4. اختر "تذكر بياناتي" لحفظ بيانات الاعتماد

### الشاشة الرئيسية
- **الرئيسية**: عرض حالة الجهاز والإجراءات السريعة
- **الأجهزة**: إدارة الأجهزة المتصلة
- **الرسائل**: نظام المراسلة
- **مشاركة الملفات**: رفع وتحميل الملفات
- **الإعدادات**: تكوين التطبيق وتسجيل الخروج

## الحالة الحالية

🟢 **مكتمل**: الهيكل الأساسي، تسجيل الدخول، الشاشة الرئيسية
🟡 **قيد التطوير**: إدارة WiFi، الأجهزة المتصلة، المراسلة
🔴 **مخطط**: مشاركة الملفات، مراقبة البيانات، الإعدادات المتقدمة

---

**ملاحظة**: هذا المشروع مطور لأغراض تعليمية ولا يرتبط رسمياً بشركة ZTE.
