# حل مشكلة بناء APK - مسار يحتوي على أقواس

## المشكلة
المسار الحالي يحتوي على أقواس `()` مما يسبب مشكلة في Gradle:
```
D:\Desktop\0000\zte\ZTELink_v3.2.6(20241021)_base_src\zte_link_flutter
```

الخطأ:
```
'D:\Desktop\0000\zte\ZTELink_v3.2.6' is not recognized as an internal or external command
```

## الحلول المقترحة

### الحل الأول: نسخ المشروع إلى مسار بسيط

1. **إنشاء مجلد جديد بدون أقواس:**
   ```cmd
   mkdir C:\ZTELink_Flutter
   ```

2. **نسخ المشروع:**
   ```cmd
   xcopy "D:\Desktop\0000\zte\ZTELink_v3.2.6(20241021)_base_src\zte_link_flutter" "C:\ZTELink_Flutter" /E /I /H
   ```

3. **الانتقال للمجلد الجديد وبناء APK:**
   ```cmd
   cd C:\ZTELink_Flutter
   flutter clean
   flutter pub get
   flutter build apk --release
   ```

### الحل الثاني: استخدام اسم مجلد مختصر

1. **إعادة تسمية المجلد الأب:**
   ```cmd
   ren "D:\Desktop\0000\zte\ZTELink_v3.2.6(20241021)_base_src" "ZTELink_src"
   ```

2. **بناء APK من المسار الجديد:**
   ```cmd
   cd "D:\Desktop\0000\zte\ZTELink_src\zte_link_flutter"
   flutter build apk --release
   ```

### الحل الثالث: استخدام PowerShell مع تعامل خاص مع المسار

```powershell
# في PowerShell
Set-Location "D:\Desktop\0000\zte\ZTELink_v3.2.6(20241021)_base_src\zte_link_flutter"
$env:FLUTTER_ROOT = "C:\flutter"
& flutter build apk --release
```

### الحل الرابع: تعديل gradle.properties

إضافة السطر التالي في `android/gradle.properties`:
```properties
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en
```

## الخطوات المفصلة للحل الأول (الموصى به)

### 1. إنشاء مجلد جديد
```cmd
mkdir C:\ZTELink_Flutter
cd C:\ZTELink_Flutter
```

### 2. نسخ الملفات
```cmd
robocopy "D:\Desktop\0000\zte\ZTELink_v3.2.6(20241021)_base_src\zte_link_flutter" "C:\ZTELink_Flutter" /E /XD build .dart_tool
```

### 3. تنظيف وإعداد المشروع
```cmd
cd C:\ZTELink_Flutter
flutter clean
flutter pub get
```

### 4. بناء APK
```cmd
flutter build apk --release
```

أو لبناء APKs منفصلة لكل معمارية:
```cmd
flutter build apk --release --split-per-abi
```

## ملفات APK المتوقعة

بعد البناء الناجح، ستجد الملفات في:
```
C:\ZTELink_Flutter\build\app\outputs\flutter-apk\
```

الملفات المتوقعة:
- `app-release.apk` (APK واحد لجميع المعماريات)
- أو ملفات منفصلة:
  - `app-arm64-v8a-release.apk` (للأجهزة الحديثة 64-bit)
  - `app-armeabi-v7a-release.apk` (للأجهزة القديمة 32-bit)
  - `app-x86_64-release.apk` (للمحاكيات)

## التحقق من نجاح البناء

```cmd
dir "C:\ZTELink_Flutter\build\app\outputs\flutter-apk\*.apk"
```

## معلومات إضافية

### حجم APK المتوقع
- APK واحد: ~15-25 MB
- APK منفصل لكل معمارية: ~8-12 MB لكل ملف

### اختبار APK
```cmd
# تثبيت على جهاز متصل
adb install "C:\ZTELink_Flutter\build\app\outputs\flutter-apk\app-release.apk"
```

### إعدادات التوقيع (للإنتاج)
لإنشاء APK موقع للنشر، أضف إعدادات التوقيع في `android/app/build.gradle.kts`:

```kotlin
android {
    signingConfigs {
        create("release") {
            keyAlias = "your-key-alias"
            keyPassword = "your-key-password"
            storeFile = file("path/to/your/keystore.jks")
            storePassword = "your-store-password"
        }
    }
    buildTypes {
        getByName("release") {
            signingConfig = signingConfigs.getByName("release")
        }
    }
}
```

## استكشاف الأخطاء

### إذا فشل البناء مرة أخرى:
1. تحقق من إصدار Flutter: `flutter --version`
2. تحقق من إصدار Android SDK: `flutter doctor`
3. تنظيف شامل: `flutter clean && flutter pub get`
4. إعادة تشغيل Android Studio/VS Code

### لوجات مفصلة:
```cmd
flutter build apk --release --verbose
```

هذا الحل سيضمن بناء APK بنجاح بدون مشاكل المسار!
