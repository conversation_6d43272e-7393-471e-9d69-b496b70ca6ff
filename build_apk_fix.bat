@echo off
echo ========================================
echo ZTELink Flutter APK Builder
echo ========================================

REM Set environment variables
set FLUTTER_ROOT=C:\flutter
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
set PATH=%FLUTTER_ROOT%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%JAVA_HOME%\bin;%PATH%

echo Current directory: %CD%
echo Flutter version:
flutter --version

echo.
echo ========================================
echo Step 1: Cleaning project...
echo ========================================
flutter clean

echo.
echo ========================================
echo Step 2: Getting dependencies...
echo ========================================
flutter pub get

echo.
echo ========================================
echo Step 3: Building APK...
echo ========================================

REM Create a temporary batch file to handle the path issue
echo @echo off > temp_build.bat
echo cd /d "%CD%" >> temp_build.bat
echo flutter build apk --release --split-per-abi >> temp_build.bat

REM Execute the temporary batch file
call temp_build.bat

REM Clean up
del temp_build.bat

echo.
echo ========================================
echo Build completed!
echo ========================================

REM Check if APK was created
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo SUCCESS: APK file created at build\app\outputs\flutter-apk\app-release.apk
    echo File size:
    dir "build\app\outputs\flutter-apk\app-release.apk"
) else (
    echo ERROR: APK file not found!
    echo Checking for split APKs...
    dir "build\app\outputs\flutter-apk\*.apk"
)

echo.
echo Press any key to exit...
pause > nul
