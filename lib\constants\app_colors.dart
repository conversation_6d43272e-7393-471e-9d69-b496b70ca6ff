import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryDark = Color(0xFF1565C0);
  static const Color primaryLight = Color(0xFF42A5F5);
  
  // Accent Colors
  static const Color accent = Color(0xFF03DAC6);
  static const Color accentDark = Color(0xFF018786);
  
  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFFFFFFF);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Connection Status Colors
  static const Color connected = Color(0xFF4CAF50);
  static const Color disconnected = Color(0xFFF44336);
  static const Color connecting = Color(0xFFFF9800);
  
  // Device Status Colors
  static const Color deviceOnline = Color(0xFF4CAF50);
  static const Color deviceOffline = Color(0xFF9E9E9E);
  static const Color deviceBlocked = Color(0xFFF44336);
  
  // Data Usage Colors
  static const Color dataUsageNormal = Color(0xFF2196F3);
  static const Color dataUsageWarning = Color(0xFFFF9800);
  static const Color dataUsageCritical = Color(0xFFF44336);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderFocused = Color(0xFF1976D2);
  
  // Shadow Colors
  static const Color shadow = Color(0x1F000000);
  static const Color shadowLight = Color(0x0F000000);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
