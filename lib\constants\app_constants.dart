class AppConstants {
  // App Information
  static const String appName = 'ZTELink';
  static const String appVersion = '3.2.6';

  // API Endpoints
  static const String baseUrl = 'http://***********';
  static const String loginEndpoint = '/api/webserver/SesTokInfo';
  static const String deviceInfoEndpoint = '/api/device/information';
  static const String wifiSettingsEndpoint = '/api/wlan/basic-settings';
  static const String connectedDevicesEndpoint = '/api/wlan/host-list';
  static const String trafficStatsEndpoint =
      '/api/monitoring/traffic-statistics';

  // Bandwidth and QoS Endpoints
  static const String bandwidthSettingsEndpoint = '/api/qos/bandwidth-settings';
  static const String trafficControlEndpoint = '/api/qos/traffic-control';
  static const String qosSettingsEndpoint = '/api/qos/settings';
  static const String speedLimitEndpoint = '/api/qos/speed-limit';
  static const String trafficMonitoringEndpoint = '/api/monitoring/traffic';
  static const String networkStatsEndpoint =
      '/api/monitoring/network-statistics';

  // Default Values
  static const String defaultUsername = 'admin';
  static const int connectionTimeout = 30000;
  static const int receiveTimeout = 30000;

  // Storage Keys
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyUsername = 'username';
  static const String keyPassword = 'password';
  static const String keyDeviceIp = 'device_ip';
  static const String keySessionToken = 'session_token';
  static const String keyRememberLogin = 'remember_login';

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;

  // Colors (will be moved to theme)
  static const int primaryColorValue = 0xFF1976D2;
  static const int accentColorValue = 0xFF03DAC6;

  // Network Settings
  static const List<String> commonDeviceIPs = [
    '***********',
    '***********',
    '***********',
    '********',
  ];

  // File Types for sharing
  static const List<String> supportedImageTypes = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'webp',
  ];

  static const List<String> supportedVideoTypes = [
    'mp4',
    'avi',
    'mkv',
    'mov',
    'wmv',
    'flv',
  ];

  static const List<String> supportedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'txt',
    'rtf',
  ];
}
