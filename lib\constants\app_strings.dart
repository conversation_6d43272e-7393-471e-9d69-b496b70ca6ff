class AppStrings {
  // App General
  static const String appName = 'ZTELink';
  static const String appDescription = 'Manage your ZTE Mi-Fi and CPE devices';

  // Authentication
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String username = 'Username';
  static const String password = 'Password';
  static const String deviceIP = 'Device IP';
  static const String rememberMe = 'Remember Me';
  static const String forgotPassword = 'Forgot Password?';
  static const String loginFailed =
      'Login failed. Please check your credentials.';
  static const String loginSuccess = 'Login successful';
  static const String pleaseEnterUsername = 'Please enter username';
  static const String pleaseEnterPassword = 'Please enter password';
  static const String pleaseEnterDeviceIP = 'Please enter device IP';

  // Navigation
  static const String home = 'Home';
  static const String devices = 'Devices';
  static const String messages = 'Messages';
  static const String fileShare = 'File Share';
  static const String settings = 'Settings';
  static const String about = 'About';

  // Home Screen
  static const String welcome = 'Welcome';
  static const String deviceStatus = 'Device Status';
  static const String connectionStatus = 'Connection Status';
  static const String connected = 'Connected';
  static const String disconnected = 'Disconnected';
  static const String connecting = 'Connecting...';

  // WiFi Management
  static const String wifiSettings = 'WiFi Settings';
  static const String hotspotSettings = 'Hotspot Settings';
  static const String ssid = 'SSID';
  static const String wifiPassword = 'WiFi Password';
  static const String securityType = 'Security Type';
  static const String enableWifi = 'Enable WiFi';
  static const String disableWifi = 'Disable WiFi';
  static const String wifiEnabled = 'WiFi Enabled';
  static const String wifiDisabled = 'WiFi Disabled';

  // Connected Devices
  static const String connectedDevices = 'Connected Devices';
  static const String deviceName = 'Device Name';
  static const String macAddress = 'MAC Address';
  static const String ipAddress = 'IP Address';
  static const String connectionTime = 'Connection Time';
  static const String dataUsage = 'Data Usage';
  static const String blockDevice = 'Block Device';
  static const String unblockDevice = 'Unblock Device';
  static const String deviceBlocked = 'Device Blocked';
  static const String deviceUnblocked = 'Device Unblocked';

  // Messages
  static const String newMessage = 'New Message';
  static const String sendMessage = 'Send Message';
  static const String messageHistory = 'Message History';
  static const String noMessages = 'No messages yet';
  static const String messageSent = 'Message sent';
  static const String messageFailed = 'Failed to send message';

  // File Sharing
  static const String shareFiles = 'Share Files';
  static const String selectFiles = 'Select Files';
  static const String uploadFiles = 'Upload Files';
  static const String downloadFiles = 'Download Files';
  static const String fileShared = 'File shared successfully';
  static const String fileSharingFailed = 'File sharing failed';
  static const String noFilesSelected = 'No files selected';

  // Data Monitoring
  static const String dataMonitoring = 'Data Monitoring';
  static const String totalUsage = 'Total Usage';
  static const String monthlyUsage = 'Monthly Usage';
  static const String dailyUsage = 'Daily Usage';
  static const String dataLimit = 'Data Limit';
  static const String setDataLimit = 'Set Data Limit';

  // Device Settings
  static const String deviceInformation = 'Device Information';
  static const String deviceModel = 'Device Model';
  static const String firmwareVersion = 'Firmware Version';
  static const String imei = 'IMEI';
  static const String imsi = 'IMSI';
  static const String signalStrength = 'Signal Strength';
  static const String batteryLevel = 'Battery Level';

  // General
  static const String ok = 'OK';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String refresh = 'Refresh';
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String success = 'Success';
  static const String warning = 'Warning';
  static const String info = 'Info';
  static const String noData = 'No data available';
  static const String retry = 'Retry';

  // Bandwidth Settings
  static const String bandwidthSettings = 'إعدادات النطاق';
  static const String bandwidthDisplay = 'حول عرض النطاق';
  static const String uploadSpeed = 'تحميل';
  static const String downloadSpeed = 'تحميل';
  static const String activate = 'تفعيل';
  static const String kbits = 'kbits';
  static const String bandwidthEnabled = 'تم تفعيل النطاق الترددي';
  static const String bandwidthDisabled = 'تم إلغاء تفعيل النطاق الترددي';
  static const String invalidSpeedValues = 'يرجى إدخال قيم صحيحة للسرعة';
  static const String bandwidthUpdateSuccess = 'تم تحديث إعدادات النطاق بنجاح';
  static const String bandwidthUpdateFailed = 'فشل في تحديث إعدادات النطاق';

  // Error Messages
  static const String networkError =
      'Network error. Please check your connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'An unknown error occurred.';
  static const String timeoutError = 'Request timeout. Please try again.';
  static const String connectionFailed = 'Failed to connect to device.';
}
