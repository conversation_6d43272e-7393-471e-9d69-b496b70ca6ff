class BandwidthModel {
  final int uploadSpeed;
  final int downloadSpeed;
  final bool isEnabled;
  final String unit;
  final DateTime lastUpdated;

  BandwidthModel({
    required this.uploadSpeed,
    required this.downloadSpeed,
    required this.isEnabled,
    this.unit = 'kbps',
    required this.lastUpdated,
  });

  factory BandwidthModel.fromJson(Map<String, dynamic> json) {
    return BandwidthModel(
      uploadSpeed: int.tryParse(json['UploadSpeed']?.toString() ?? '0') ?? 0,
      downloadSpeed: int.tryParse(json['DownloadSpeed']?.toString() ?? '0') ?? 0,
      isEnabled: json['BandwidthEnabled'] == '1' || json['BandwidthEnabled'] == true,
      unit: json['Unit'] ?? 'kbps',
      lastUpdated: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'UploadSpeed': uploadSpeed.toString(),
      'DownloadSpeed': downloadSpeed.toString(),
      'BandwidthEnabled': isEnabled ? '1' : '0',
      'Unit': unit,
      'LastUpdated': lastUpdated.toIso8601String(),
    };
  }

  BandwidthModel copyWith({
    int? uploadSpeed,
    int? downloadSpeed,
    bool? isEnabled,
    String? unit,
    DateTime? lastUpdated,
  }) {
    return BandwidthModel(
      uploadSpeed: uploadSpeed ?? this.uploadSpeed,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      isEnabled: isEnabled ?? this.isEnabled,
      unit: unit ?? this.unit,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // Get formatted speed string
  String getFormattedUploadSpeed() {
    return _formatSpeed(uploadSpeed);
  }

  String getFormattedDownloadSpeed() {
    return _formatSpeed(downloadSpeed);
  }

  String _formatSpeed(int speedKbits) {
    if (speedKbits < 1000) {
      return '$speedKbits Kbps';
    } else if (speedKbits < 1000000) {
      return '${(speedKbits / 1000).toStringAsFixed(1)} Mbps';
    } else {
      return '${(speedKbits / 1000000).toStringAsFixed(2)} Gbps';
    }
  }

  // Validation methods
  bool get isValidUploadSpeed => uploadSpeed > 0 && uploadSpeed <= 1000000;
  bool get isValidDownloadSpeed => downloadSpeed > 0 && downloadSpeed <= 1000000;
  bool get isValid => isValidUploadSpeed && isValidDownloadSpeed;

  // Speed comparison methods
  bool get isHighSpeed => uploadSpeed >= 10000 || downloadSpeed >= 10000; // 10 Mbps
  bool get isMediumSpeed => (uploadSpeed >= 1000 || downloadSpeed >= 1000) && !isHighSpeed; // 1-10 Mbps
  bool get isLowSpeed => !isMediumSpeed && !isHighSpeed; // < 1 Mbps

  @override
  String toString() {
    return 'BandwidthModel(upload: ${getFormattedUploadSpeed()}, download: ${getFormattedDownloadSpeed()}, enabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BandwidthModel &&
        other.uploadSpeed == uploadSpeed &&
        other.downloadSpeed == downloadSpeed &&
        other.isEnabled == isEnabled &&
        other.unit == unit;
  }

  @override
  int get hashCode {
    return uploadSpeed.hashCode ^
        downloadSpeed.hashCode ^
        isEnabled.hashCode ^
        unit.hashCode;
  }
}
