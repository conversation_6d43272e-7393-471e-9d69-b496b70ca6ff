class ConnectedDevice {

  ConnectedDevice({
    required this.id,
    required this.deviceName,
    required this.macAddress,
    required this.ipAddress,
    required this.deviceType,
    required this.connectionTime,
    required this.dataUsage,
    required this.isBlocked,
    required this.signalStrength,
    required this.hostName,
    required this.isActive,
  });

  factory ConnectedDevice.fromJson(Map<String, dynamic> json) {
    return ConnectedDevice(
      id: json['ID']?.toString() ?? '',
      deviceName: json['HostName'] ?? json['DeviceName'] ?? 'Unknown Device',
      macAddress: json['MacAddress'] ?? '',
      ipAddress: json['IpAddress'] ?? '',
      deviceType: _getDeviceType(json['HostName'] ?? ''),
      connectionTime: _parseConnectionTime(json['AssociatedTime']),
      dataUsage: int.tryParse(json['TotalBytes']?.toString() ?? '0') ?? 0,
      isBlocked: json['IsBlocked'] == '1' || json['IsBlocked'] == true,
      signalStrength: int.tryParse(json['SignalStrength']?.toString() ?? '0') ?? 0,
      hostName: json['HostName'] ?? '',
      isActive: json['Active'] == '1' || json['Active'] == true,
    );
  }
  final String id;
  final String deviceName;
  final String macAddress;
  final String ipAddress;
  final String deviceType;
  final DateTime connectionTime;
  final int dataUsage; // in bytes
  final bool isBlocked;
  final int signalStrength;
  final String hostName;
  final bool isActive;

  Map<String, dynamic> toJson() {
    return {
      'ID': id,
      'HostName': deviceName,
      'MacAddress': macAddress,
      'IpAddress': ipAddress,
      'DeviceType': deviceType,
      'AssociatedTime': connectionTime.millisecondsSinceEpoch.toString(),
      'TotalBytes': dataUsage.toString(),
      'IsBlocked': isBlocked ? '1' : '0',
      'SignalStrength': signalStrength.toString(),
      'Active': isActive ? '1' : '0',
    };
  }

  static String _getDeviceType(String hostName) {
    final lowerHostName = hostName.toLowerCase();
    if (lowerHostName.contains('android') || lowerHostName.contains('samsung') || 
        lowerHostName.contains('huawei') || lowerHostName.contains('xiaomi')) {
      return 'Android';
    } else if (lowerHostName.contains('iphone') || lowerHostName.contains('ipad') || 
               lowerHostName.contains('apple')) {
      return 'iOS';
    } else if (lowerHostName.contains('windows') || lowerHostName.contains('pc')) {
      return 'Windows';
    } else if (lowerHostName.contains('mac') || lowerHostName.contains('macbook')) {
      return 'macOS';
    } else if (lowerHostName.contains('linux') || lowerHostName.contains('ubuntu')) {
      return 'Linux';
    } else {
      return 'Unknown';
    }
  }

  static DateTime _parseConnectionTime(dynamic timeStr) {
    if (timeStr == null) return DateTime.now();
    
    try {
      if (timeStr is String) {
        // Try parsing as milliseconds since epoch
        final timestamp = int.tryParse(timeStr);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
        
        // Try parsing as ISO string
        return DateTime.parse(timeStr);
      } else if (timeStr is int) {
        return DateTime.fromMillisecondsSinceEpoch(timeStr);
      }
    } catch (e) {
      // If parsing fails, return current time
    }
    
    return DateTime.now();
  }

  String get formattedDataUsage {
    if (dataUsage < 1024) {
      return '${dataUsage}B';
    } else if (dataUsage < 1024 * 1024) {
      return '${(dataUsage / 1024).toStringAsFixed(1)}KB';
    } else if (dataUsage < 1024 * 1024 * 1024) {
      return '${(dataUsage / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(dataUsage / (1024 * 1024 * 1024)).toStringAsFixed(2)}GB';
    }
  }

  String get formattedConnectionTime {
    final now = DateTime.now();
    final difference = now.difference(connectionTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  ConnectedDevice copyWith({
    String? id,
    String? deviceName,
    String? macAddress,
    String? ipAddress,
    String? deviceType,
    DateTime? connectionTime,
    int? dataUsage,
    bool? isBlocked,
    int? signalStrength,
    String? hostName,
    bool? isActive,
  }) {
    return ConnectedDevice(
      id: id ?? this.id,
      deviceName: deviceName ?? this.deviceName,
      macAddress: macAddress ?? this.macAddress,
      ipAddress: ipAddress ?? this.ipAddress,
      deviceType: deviceType ?? this.deviceType,
      connectionTime: connectionTime ?? this.connectionTime,
      dataUsage: dataUsage ?? this.dataUsage,
      isBlocked: isBlocked ?? this.isBlocked,
      signalStrength: signalStrength ?? this.signalStrength,
      hostName: hostName ?? this.hostName,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'ConnectedDevice(id: $id, deviceName: $deviceName, macAddress: $macAddress, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConnectedDevice &&
        other.id == id &&
        other.macAddress == macAddress;
  }

  @override
  int get hashCode {
    return id.hashCode ^ macAddress.hashCode;
  }
}
