class DeviceInfo {

  DeviceInfo({
    required this.deviceName,
    required this.model,
    required this.firmwareVersion,
    required this.hardwareVersion,
    required this.imei,
    required this.imsi,
    required this.macAddress,
    required this.ipAddress,
    required this.signalStrength,
    required this.batteryLevel,
    required this.isCharging,
    required this.networkType,
    required this.operator,
    required this.lastUpdated,
  });

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      deviceName: json['DeviceName'] ?? '',
      model: json['Model'] ?? '',
      firmwareVersion: json['SoftwareVersion'] ?? '',
      hardwareVersion: json['HardwareVersion'] ?? '',
      imei: json['Imei'] ?? '',
      imsi: json['Imsi'] ?? '',
      macAddress: json['MacAddress'] ?? '',
      ipAddress: json['IpAddress'] ?? '',
      signalStrength: int.tryParse(json['SignalStrength']?.toString() ?? '0') ?? 0,
      batteryLevel: int.tryParse(json['BatteryLevel']?.toString() ?? '0') ?? 0,
      isCharging: json['BatteryStatus'] == '1',
      networkType: json['NetworkType'] ?? '',
      operator: json['CurrentNetworkType'] ?? '',
      lastUpdated: DateTime.now(),
    );
  }
  final String deviceName;
  final String model;
  final String firmwareVersion;
  final String hardwareVersion;
  final String imei;
  final String imsi;
  final String macAddress;
  final String ipAddress;
  final int signalStrength;
  final int batteryLevel;
  final bool isCharging;
  final String networkType;
  final String operator;
  final DateTime lastUpdated;

  Map<String, dynamic> toJson() {
    return {
      'DeviceName': deviceName,
      'Model': model,
      'SoftwareVersion': firmwareVersion,
      'HardwareVersion': hardwareVersion,
      'Imei': imei,
      'Imsi': imsi,
      'MacAddress': macAddress,
      'IpAddress': ipAddress,
      'SignalStrength': signalStrength.toString(),
      'BatteryLevel': batteryLevel.toString(),
      'BatteryStatus': isCharging ? '1' : '0',
      'NetworkType': networkType,
      'CurrentNetworkType': operator,
    };
  }

  DeviceInfo copyWith({
    String? deviceName,
    String? model,
    String? firmwareVersion,
    String? hardwareVersion,
    String? imei,
    String? imsi,
    String? macAddress,
    String? ipAddress,
    int? signalStrength,
    int? batteryLevel,
    bool? isCharging,
    String? networkType,
    String? operator,
    DateTime? lastUpdated,
  }) {
    return DeviceInfo(
      deviceName: deviceName ?? this.deviceName,
      model: model ?? this.model,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
      hardwareVersion: hardwareVersion ?? this.hardwareVersion,
      imei: imei ?? this.imei,
      imsi: imsi ?? this.imsi,
      macAddress: macAddress ?? this.macAddress,
      ipAddress: ipAddress ?? this.ipAddress,
      signalStrength: signalStrength ?? this.signalStrength,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      isCharging: isCharging ?? this.isCharging,
      networkType: networkType ?? this.networkType,
      operator: operator ?? this.operator,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'DeviceInfo(deviceName: $deviceName, model: $model, firmwareVersion: $firmwareVersion)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceInfo &&
        other.deviceName == deviceName &&
        other.model == model &&
        other.firmwareVersion == firmwareVersion &&
        other.imei == imei;
  }

  @override
  int get hashCode {
    return deviceName.hashCode ^
        model.hashCode ^
        firmwareVersion.hashCode ^
        imei.hashCode;
  }
}
