class TrafficStatistics {
  final int totalUpload;
  final int totalDownload;
  final int currentUploadSpeed;
  final int currentDownloadSpeed;
  final int monthlyUpload;
  final int monthlyDownload;
  final int dailyUpload;
  final int dailyDownload;
  final DateTime lastUpdated;
  final String connectionType;
  final int connectedDevices;

  TrafficStatistics({
    required this.totalUpload,
    required this.totalDownload,
    required this.currentUploadSpeed,
    required this.currentDownloadSpeed,
    required this.monthlyUpload,
    required this.monthlyDownload,
    required this.dailyUpload,
    required this.dailyDownload,
    required this.lastUpdated,
    required this.connectionType,
    required this.connectedDevices,
  });

  factory TrafficStatistics.fromJson(Map<String, dynamic> json) {
    return TrafficStatistics(
      totalUpload: int.tryParse(json['TotalUpload']?.toString() ?? '0') ?? 0,
      totalDownload: int.tryParse(json['TotalDownload']?.toString() ?? '0') ?? 0,
      currentUploadSpeed: int.tryParse(json['CurrentUploadSpeed']?.toString() ?? '0') ?? 0,
      currentDownloadSpeed: int.tryParse(json['CurrentDownloadSpeed']?.toString() ?? '0') ?? 0,
      monthlyUpload: int.tryParse(json['MonthlyUpload']?.toString() ?? '0') ?? 0,
      monthlyDownload: int.tryParse(json['MonthlyDownload']?.toString() ?? '0') ?? 0,
      dailyUpload: int.tryParse(json['DailyUpload']?.toString() ?? '0') ?? 0,
      dailyDownload: int.tryParse(json['DailyDownload']?.toString() ?? '0') ?? 0,
      lastUpdated: DateTime.now(),
      connectionType: json['ConnectionType'] ?? 'Unknown',
      connectedDevices: int.tryParse(json['ConnectedDevices']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'TotalUpload': totalUpload.toString(),
      'TotalDownload': totalDownload.toString(),
      'CurrentUploadSpeed': currentUploadSpeed.toString(),
      'CurrentDownloadSpeed': currentDownloadSpeed.toString(),
      'MonthlyUpload': monthlyUpload.toString(),
      'MonthlyDownload': monthlyDownload.toString(),
      'DailyUpload': dailyUpload.toString(),
      'DailyDownload': dailyDownload.toString(),
      'ConnectionType': connectionType,
      'ConnectedDevices': connectedDevices.toString(),
      'LastUpdated': lastUpdated.toIso8601String(),
    };
  }

  // Formatted data methods
  String get formattedTotalUpload => _formatBytes(totalUpload);
  String get formattedTotalDownload => _formatBytes(totalDownload);
  String get formattedMonthlyUpload => _formatBytes(monthlyUpload);
  String get formattedMonthlyDownload => _formatBytes(monthlyDownload);
  String get formattedDailyUpload => _formatBytes(dailyUpload);
  String get formattedDailyDownload => _formatBytes(dailyDownload);
  
  String get formattedCurrentUploadSpeed => _formatSpeed(currentUploadSpeed);
  String get formattedCurrentDownloadSpeed => _formatSpeed(currentDownloadSpeed);

  String get formattedTotalData => _formatBytes(totalUpload + totalDownload);
  String get formattedMonthlyData => _formatBytes(monthlyUpload + monthlyDownload);
  String get formattedDailyData => _formatBytes(dailyUpload + dailyDownload);

  // Helper methods
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }

  String _formatSpeed(int bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '$bytesPerSecond B/s';
    } else if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }

  // Calculate usage percentages (if limits are set)
  double getUploadPercentage(int limit) {
    if (limit <= 0) return 0.0;
    return (totalUpload / limit).clamp(0.0, 1.0);
  }

  double getDownloadPercentage(int limit) {
    if (limit <= 0) return 0.0;
    return (totalDownload / limit).clamp(0.0, 1.0);
  }

  double getTotalPercentage(int limit) {
    if (limit <= 0) return 0.0;
    return ((totalUpload + totalDownload) / limit).clamp(0.0, 1.0);
  }

  // Check if usage is approaching limits
  bool isApproachingLimit(int limit, {double threshold = 0.8}) {
    return getTotalPercentage(limit) >= threshold;
  }

  bool isOverLimit(int limit) {
    return getTotalPercentage(limit) >= 1.0;
  }

  TrafficStatistics copyWith({
    int? totalUpload,
    int? totalDownload,
    int? currentUploadSpeed,
    int? currentDownloadSpeed,
    int? monthlyUpload,
    int? monthlyDownload,
    int? dailyUpload,
    int? dailyDownload,
    DateTime? lastUpdated,
    String? connectionType,
    int? connectedDevices,
  }) {
    return TrafficStatistics(
      totalUpload: totalUpload ?? this.totalUpload,
      totalDownload: totalDownload ?? this.totalDownload,
      currentUploadSpeed: currentUploadSpeed ?? this.currentUploadSpeed,
      currentDownloadSpeed: currentDownloadSpeed ?? this.currentDownloadSpeed,
      monthlyUpload: monthlyUpload ?? this.monthlyUpload,
      monthlyDownload: monthlyDownload ?? this.monthlyDownload,
      dailyUpload: dailyUpload ?? this.dailyUpload,
      dailyDownload: dailyDownload ?? this.dailyDownload,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      connectionType: connectionType ?? this.connectionType,
      connectedDevices: connectedDevices ?? this.connectedDevices,
    );
  }

  @override
  String toString() {
    return 'TrafficStatistics(total: ${formattedTotalData}, speed: ${formattedCurrentUploadSpeed}↑/${formattedCurrentDownloadSpeed}↓)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrafficStatistics &&
        other.totalUpload == totalUpload &&
        other.totalDownload == totalDownload &&
        other.currentUploadSpeed == currentUploadSpeed &&
        other.currentDownloadSpeed == currentDownloadSpeed;
  }

  @override
  int get hashCode {
    return totalUpload.hashCode ^
        totalDownload.hashCode ^
        currentUploadSpeed.hashCode ^
        currentDownloadSpeed.hashCode;
  }
}

// QoS Settings Model
class QoSSettings {
  final bool enabled;
  final String mode; // 'auto', 'manual'
  final int highPriority;
  final int mediumPriority;
  final int lowPriority;
  final Map<String, int> devicePriorities;
  final DateTime lastUpdated;

  QoSSettings({
    required this.enabled,
    required this.mode,
    required this.highPriority,
    required this.mediumPriority,
    required this.lowPriority,
    required this.devicePriorities,
    required this.lastUpdated,
  });

  factory QoSSettings.fromJson(Map<String, dynamic> json) {
    return QoSSettings(
      enabled: json['QoSEnabled'] == '1' || json['QoSEnabled'] == true,
      mode: json['QoSMode'] ?? 'auto',
      highPriority: int.tryParse(json['HighPriority']?.toString() ?? '70') ?? 70,
      mediumPriority: int.tryParse(json['MediumPriority']?.toString() ?? '50') ?? 50,
      lowPriority: int.tryParse(json['LowPriority']?.toString() ?? '30') ?? 30,
      devicePriorities: Map<String, int>.from(json['DevicePriorities'] ?? {}),
      lastUpdated: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'QoSEnabled': enabled ? '1' : '0',
      'QoSMode': mode,
      'HighPriority': highPriority.toString(),
      'MediumPriority': mediumPriority.toString(),
      'LowPriority': lowPriority.toString(),
      'DevicePriorities': devicePriorities,
      'LastUpdated': lastUpdated.toIso8601String(),
    };
  }

  QoSSettings copyWith({
    bool? enabled,
    String? mode,
    int? highPriority,
    int? mediumPriority,
    int? lowPriority,
    Map<String, int>? devicePriorities,
    DateTime? lastUpdated,
  }) {
    return QoSSettings(
      enabled: enabled ?? this.enabled,
      mode: mode ?? this.mode,
      highPriority: highPriority ?? this.highPriority,
      mediumPriority: mediumPriority ?? this.mediumPriority,
      lowPriority: lowPriority ?? this.lowPriority,
      devicePriorities: devicePriorities ?? this.devicePriorities,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
