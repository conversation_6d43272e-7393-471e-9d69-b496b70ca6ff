enum WifiSecurityType {
  none,
  wep,
  wpa,
  wpa2,
  wpa3,
}

enum WifiFrequency {
  freq24GHz,
  freq5GHz,
  dual,
}

class WifiSettings {

  WifiSettings({
    required this.ssid,
    required this.password,
    required this.securityType,
    required this.frequency,
    required this.isEnabled,
    required this.isHidden,
    required this.maxConnections,
    required this.channel,
    required this.countryCode,
    required this.wpsEnabled,
    required this.signalStrength,
    required this.lastUpdated,
  });

  factory WifiSettings.fromJson(Map<String, dynamic> json) {
    return WifiSettings(
      ssid: json['WifiSsid'] ?? json['SSID'] ?? '',
      password: json['WifiPassword'] ?? json['WifiKey'] ?? '',
      securityType: _parseSecurityType(json['AuthMode'] ?? json['SecurityType']),
      frequency: _parseFrequency(json['WifiFrequency'] ?? json['Frequency']),
      isEnabled: json['WifiEnable'] == '1' || json['WifiEnable'] == true,
      isHidden: json['WifiHide'] == '1' || json['WifiHide'] == true,
      maxConnections: int.tryParse(json['MaxAssoc']?.toString() ?? '10') ?? 10,
      channel: int.tryParse(json['WifiChannel']?.toString() ?? '0') ?? 0,
      countryCode: json['CountryCode'] ?? 'US',
      wpsEnabled: json['WpsEnable'] == '1' || json['WpsEnable'] == true,
      signalStrength: int.tryParse(json['SignalStrength']?.toString() ?? '0') ?? 0,
      lastUpdated: DateTime.now(),
    );
  }
  final String ssid;
  final String password;
  final WifiSecurityType securityType;
  final WifiFrequency frequency;
  final bool isEnabled;
  final bool isHidden;
  final int maxConnections;
  final int channel;
  final String countryCode;
  final bool wpsEnabled;
  final int signalStrength;
  final DateTime lastUpdated;

  Map<String, dynamic> toJson() {
    return {
      'WifiSsid': ssid,
      'WifiPassword': password,
      'AuthMode': _securityTypeToString(securityType),
      'WifiFrequency': _frequencyToString(frequency),
      'WifiEnable': isEnabled ? '1' : '0',
      'WifiHide': isHidden ? '1' : '0',
      'MaxAssoc': maxConnections.toString(),
      'WifiChannel': channel.toString(),
      'CountryCode': countryCode,
      'WpsEnable': wpsEnabled ? '1' : '0',
      'SignalStrength': signalStrength.toString(),
    };
  }

  static WifiSecurityType _parseSecurityType(dynamic securityType) {
    if (securityType == null) return WifiSecurityType.wpa2;
    
    final typeStr = securityType.toString().toLowerCase();
    if (typeStr.contains('none') || typeStr.contains('open')) {
      return WifiSecurityType.none;
    } else if (typeStr.contains('wep')) {
      return WifiSecurityType.wep;
    } else if (typeStr.contains('wpa3')) {
      return WifiSecurityType.wpa3;
    } else if (typeStr.contains('wpa2')) {
      return WifiSecurityType.wpa2;
    } else if (typeStr.contains('wpa')) {
      return WifiSecurityType.wpa;
    } else {
      return WifiSecurityType.wpa2;
    }
  }

  static WifiFrequency _parseFrequency(dynamic frequency) {
    if (frequency == null) return WifiFrequency.freq24GHz;
    
    final freqStr = frequency.toString().toLowerCase();
    if (freqStr.contains('5') || freqStr.contains('5g')) {
      return WifiFrequency.freq5GHz;
    } else if (freqStr.contains('dual') || freqStr.contains('both')) {
      return WifiFrequency.dual;
    } else {
      return WifiFrequency.freq24GHz;
    }
  }

  static String _securityTypeToString(WifiSecurityType type) {
    switch (type) {
      case WifiSecurityType.none:
        return 'NONE';
      case WifiSecurityType.wep:
        return 'WEP';
      case WifiSecurityType.wpa:
        return 'WPA';
      case WifiSecurityType.wpa2:
        return 'WPA2PSK';
      case WifiSecurityType.wpa3:
        return 'WPA3PSK';
    }
  }

  static String _frequencyToString(WifiFrequency frequency) {
    switch (frequency) {
      case WifiFrequency.freq24GHz:
        return '2.4GHz';
      case WifiFrequency.freq5GHz:
        return '5GHz';
      case WifiFrequency.dual:
        return 'Dual';
    }
  }

  String get securityTypeDisplayName {
    switch (securityType) {
      case WifiSecurityType.none:
        return 'None (Open)';
      case WifiSecurityType.wep:
        return 'WEP';
      case WifiSecurityType.wpa:
        return 'WPA';
      case WifiSecurityType.wpa2:
        return 'WPA2';
      case WifiSecurityType.wpa3:
        return 'WPA3';
    }
  }

  String get frequencyDisplayName {
    switch (frequency) {
      case WifiFrequency.freq24GHz:
        return '2.4 GHz';
      case WifiFrequency.freq5GHz:
        return '5 GHz';
      case WifiFrequency.dual:
        return '2.4/5 GHz';
    }
  }

  WifiSettings copyWith({
    String? ssid,
    String? password,
    WifiSecurityType? securityType,
    WifiFrequency? frequency,
    bool? isEnabled,
    bool? isHidden,
    int? maxConnections,
    int? channel,
    String? countryCode,
    bool? wpsEnabled,
    int? signalStrength,
    DateTime? lastUpdated,
  }) {
    return WifiSettings(
      ssid: ssid ?? this.ssid,
      password: password ?? this.password,
      securityType: securityType ?? this.securityType,
      frequency: frequency ?? this.frequency,
      isEnabled: isEnabled ?? this.isEnabled,
      isHidden: isHidden ?? this.isHidden,
      maxConnections: maxConnections ?? this.maxConnections,
      channel: channel ?? this.channel,
      countryCode: countryCode ?? this.countryCode,
      wpsEnabled: wpsEnabled ?? this.wpsEnabled,
      signalStrength: signalStrength ?? this.signalStrength,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'WifiSettings(ssid: $ssid, securityType: $securityType, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WifiSettings &&
        other.ssid == ssid &&
        other.password == password &&
        other.securityType == securityType;
  }

  @override
  int get hashCode {
    return ssid.hashCode ^ password.hashCode ^ securityType.hashCode;
  }
}
