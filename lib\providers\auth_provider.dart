import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../constants/app_constants.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  AuthState _state = AuthState.initial;
  String? _username;
  String? _deviceIp;
  String? _errorMessage;
  bool _rememberLogin = false;

  // Getters
  AuthState get state => _state;
  String? get username => _username;
  String? get deviceIp => _deviceIp;
  String? get errorMessage => _errorMessage;
  bool get rememberLogin => _rememberLogin;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isLoading => _state == AuthState.loading;

  // Initialize auth provider
  Future<void> initialize() async {
    try {
      await _storageService.initialize();
      await _checkStoredCredentials();
    } catch (e) {
      print('[Auth Provider] Initialization error: $e');
      _setState(AuthState.unauthenticated);
    }
  }

  // Check for stored credentials and auto-login
  Future<void> _checkStoredCredentials() async {
    try {
      final credentials = await _storageService.getLoginCredentials();
      if (credentials != null && credentials['rememberLogin'] == true) {
        _username = credentials['username'];
        _deviceIp = credentials['deviceIp'];
        _rememberLogin = true;
        
        // Initialize API service with stored device IP
        _apiService.initialize(baseUrl: 'http://$_deviceIp');
        
        // Try to login with stored credentials
        final success = await _apiService.login(
          credentials['username'],
          credentials['password'],
        );
        
        if (success) {
          _setState(AuthState.authenticated);
        } else {
          _setState(AuthState.unauthenticated);
        }
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      print('[Auth Provider] Check stored credentials error: $e');
      _setState(AuthState.unauthenticated);
    }
  }

  // Login method
  Future<bool> login({
    required String username,
    required String password,
    required String deviceIp,
    bool rememberLogin = false,
  }) async {
    try {
      _setState(AuthState.loading);
      _clearError();

      // Validate inputs
      if (username.isEmpty) {
        _setError('Please enter username');
        _setState(AuthState.error);
        return false;
      }

      if (password.isEmpty) {
        _setError('Please enter password');
        _setState(AuthState.error);
        return false;
      }

      if (deviceIp.isEmpty) {
        _setError('Please enter device IP');
        _setState(AuthState.error);
        return false;
      }

      // Initialize API service with device IP
      final baseUrl = deviceIp.startsWith('http') ? deviceIp : 'http://$deviceIp';
      _apiService.initialize(baseUrl: baseUrl);

      // Attempt login
      final success = await _apiService.login(username, password);

      if (success) {
        _username = username;
        _deviceIp = deviceIp;
        _rememberLogin = rememberLogin;

        // Save credentials if remember login is enabled
        if (rememberLogin) {
          await _storageService.saveLoginCredentials(
            username: username,
            password: password,
            deviceIp: deviceIp,
            rememberLogin: rememberLogin,
          );
        }

        // Add to recent device IPs
        await _storageService.addRecentDeviceIP(deviceIp);

        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError('Login failed. Please check your credentials and device connection.');
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      print('[Auth Provider] Login error: $e');
      _setError('Connection failed. Please check device IP and network connection.');
      _setState(AuthState.error);
      return false;
    }
  }

  // Logout method
  Future<void> logout() async {
    try {
      _setState(AuthState.loading);

      // Call API logout
      await _apiService.logout();

      // Clear stored credentials if not remembering login
      if (!_rememberLogin) {
        await _storageService.clearLoginCredentials();
      } else {
        // Just mark as logged out but keep credentials
        await _storageService.setLoggedIn(false);
      }

      // Reset state
      _username = null;
      _deviceIp = null;
      _clearError();

      _setState(AuthState.unauthenticated);
    } catch (e) {
      print('[Auth Provider] Logout error: $e');
      // Even if logout fails, clear local state
      _setState(AuthState.unauthenticated);
    }
  }

  // Update remember login preference
  void setRememberLogin(bool remember) {
    _rememberLogin = remember;
    notifyListeners();
  }

  // Get recent device IPs
  List<String> getRecentDeviceIPs() {
    final recentIPs = _storageService.getRecentDeviceIPs();
    const commonIPs = AppConstants.commonDeviceIPs;
    
    // Combine recent and common IPs, removing duplicates
    final allIPs = <String>[];
    allIPs.addAll(recentIPs);
    
    for (final ip in commonIPs) {
      if (!allIPs.contains(ip)) {
        allIPs.add(ip);
      }
    }
    
    return allIPs;
  }

  // Check connection to device
  Future<bool> checkDeviceConnection(String deviceIp) async {
    try {
      final baseUrl = deviceIp.startsWith('http') ? deviceIp : 'http://$deviceIp';
      _apiService.initialize(baseUrl: baseUrl);
      
      // Try to get device info as a connection test
      final deviceInfo = await _apiService.getDeviceInfo();
      return deviceInfo != null;
    } catch (e) {
      print('[Auth Provider] Device connection check error: $e');
      return false;
    }
  }

  // Private helper methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Reset auth state (useful for testing or manual reset)
  Future<void> reset() async {
    try {
      await _storageService.clearAllData();
      _username = null;
      _deviceIp = null;
      _rememberLogin = false;
      _clearError();
      _setState(AuthState.initial);
    } catch (e) {
      print('[Auth Provider] Reset error: $e');
    }
  }

}
