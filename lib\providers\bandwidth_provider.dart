import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class BandwidthSettings {
  final int uploadSpeed;
  final int downloadSpeed;
  final bool isEnabled;
  final DateTime lastUpdated;

  BandwidthSettings({
    required this.uploadSpeed,
    required this.downloadSpeed,
    required this.isEnabled,
    required this.lastUpdated,
  });

  factory BandwidthSettings.fromJson(Map<String, dynamic> json) {
    return BandwidthSettings(
      uploadSpeed: int.tryParse(json['UploadSpeed']?.toString() ?? '0') ?? 0,
      downloadSpeed:
          int.tryParse(json['DownloadSpeed']?.toString() ?? '0') ?? 0,
      isEnabled:
          json['BandwidthEnabled'] == '1' || json['BandwidthEnabled'] == true,
      lastUpdated: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'UploadSpeed': uploadSpeed.toString(),
      'DownloadSpeed': downloadSpeed.toString(),
      'BandwidthEnabled': isEnabled ? '1' : '0',
    };
  }

  BandwidthSettings copyWith({
    int? uploadSpeed,
    int? downloadSpeed,
    bool? isEnabled,
    DateTime? lastUpdated,
  }) {
    return BandwidthSettings(
      uploadSpeed: uploadSpeed ?? this.uploadSpeed,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      isEnabled: isEnabled ?? this.isEnabled,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

enum BandwidthState { initial, loading, loaded, error }

class BandwidthProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  BandwidthState _state = BandwidthState.initial;
  BandwidthSettings? _settings;
  String? _errorMessage;

  // Getters
  BandwidthState get state => _state;
  BandwidthSettings? get settings => _settings;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == BandwidthState.loading;

  // Initialize provider
  Future<void> initialize() async {
    await _loadStoredSettings();
    await refreshSettings();
  }

  // Load stored settings from local storage
  Future<void> _loadStoredSettings() async {
    try {
      final storedData = _storageService.getAppSetting<Map<String, dynamic>>(
        'bandwidth_settings',
      );
      if (storedData != null) {
        _settings = BandwidthSettings.fromJson(storedData);
        _setState(BandwidthState.loaded);
      }
    } catch (e) {
      print('[Bandwidth Provider] Load stored settings error: $e');
    }
  }

  // Refresh settings from device
  Future<void> refreshSettings() async {
    try {
      _setState(BandwidthState.loading);
      _clearError();

      // Get bandwidth settings from device API
      final apiData = await _apiService.getBandwidthSettings();

      if (apiData != null) {
        _settings = BandwidthSettings.fromJson(apiData);
      } else {
        // Fallback to default values if API fails
        _settings = BandwidthSettings(
          uploadSpeed: 1000,
          downloadSpeed: 1000,
          isEnabled: true,
          lastUpdated: DateTime.now(),
        );
      }

      // Save to local storage
      await _storageService.saveAppSetting(
        'bandwidth_settings',
        _settings!.toJson(),
      );

      _setState(BandwidthState.loaded);
    } catch (e) {
      print('[Bandwidth Provider] Refresh settings error: $e');
      _setError('Failed to load bandwidth settings');
      _setState(BandwidthState.error);
    }
  }

  // Update bandwidth settings
  Future<bool> updateSettings({
    required int uploadSpeed,
    required int downloadSpeed,
    required bool isEnabled,
  }) async {
    try {
      _setState(BandwidthState.loading);
      _clearError();

      // Validate input
      if (uploadSpeed <= 0 || downloadSpeed <= 0) {
        _setError('Invalid speed values');
        _setState(BandwidthState.error);
        return false;
      }

      // Call device API to update bandwidth settings
      final success = await _apiService.setBandwidthSettings(
        uploadSpeed: uploadSpeed,
        downloadSpeed: downloadSpeed,
        enabled: isEnabled,
      );

      if (!success) {
        _setError('Failed to update device settings');
        _setState(BandwidthState.error);
        return false;
      }

      // Update local settings
      _settings = BandwidthSettings(
        uploadSpeed: uploadSpeed,
        downloadSpeed: downloadSpeed,
        isEnabled: isEnabled,
        lastUpdated: DateTime.now(),
      );

      // Save to local storage
      await _storageService.saveAppSetting(
        'bandwidth_settings',
        _settings!.toJson(),
      );

      _setState(BandwidthState.loaded);
      return true;
    } catch (e) {
      print('[Bandwidth Provider] Update settings error: $e');
      _setError('Failed to update bandwidth settings');
      _setState(BandwidthState.error);
      return false;
    }
  }

  // Enable/disable bandwidth control
  Future<bool> toggleBandwidthControl(bool enabled) async {
    if (_settings == null) return false;

    return await updateSettings(
      uploadSpeed: _settings!.uploadSpeed,
      downloadSpeed: _settings!.downloadSpeed,
      isEnabled: enabled,
    );
  }

  // Set upload speed only
  Future<bool> setUploadSpeed(int speed) async {
    if (_settings == null) return false;

    return await updateSettings(
      uploadSpeed: speed,
      downloadSpeed: _settings!.downloadSpeed,
      isEnabled: _settings!.isEnabled,
    );
  }

  // Set download speed only
  Future<bool> setDownloadSpeed(int speed) async {
    if (_settings == null) return false;

    return await updateSettings(
      uploadSpeed: _settings!.uploadSpeed,
      downloadSpeed: speed,
      isEnabled: _settings!.isEnabled,
    );
  }

  // Reset to default settings
  Future<bool> resetToDefaults() async {
    return await updateSettings(
      uploadSpeed: 1000,
      downloadSpeed: 1000,
      isEnabled: true,
    );
  }

  // Get formatted speed string
  String getFormattedSpeed(int speedKbits) {
    if (speedKbits < 1000) {
      return '${speedKbits} Kbps';
    } else if (speedKbits < 1000000) {
      return '${(speedKbits / 1000).toStringAsFixed(1)} Mbps';
    } else {
      return '${(speedKbits / 1000000).toStringAsFixed(2)} Gbps';
    }
  }

  // Private helper methods
  void _setState(BandwidthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
