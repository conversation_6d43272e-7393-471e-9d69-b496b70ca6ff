import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../models/traffic_statistics.dart';

enum TrafficState {
  initial,
  loading,
  loaded,
  error,
}

class TrafficProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  TrafficState _state = TrafficState.initial;
  TrafficStatistics? _statistics;
  QoSSettings? _qosSettings;
  String? _errorMessage;
  Timer? _refreshTimer;

  // Getters
  TrafficState get state => _state;
  TrafficStatistics? get statistics => _statistics;
  QoSSettings? get qosSettings => _qosSettings;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == TrafficState.loading;

  // Initialize provider
  Future<void> initialize() async {
    await _loadStoredData();
    await refreshData();
    _startAutoRefresh();
  }

  // Load stored data from local storage
  Future<void> _loadStoredData() async {
    try {
      final statsData = _storageService.getAppSetting<Map<String, dynamic>>('traffic_statistics');
      if (statsData != null) {
        _statistics = TrafficStatistics.fromJson(statsData);
      }

      final qosData = _storageService.getAppSetting<Map<String, dynamic>>('qos_settings');
      if (qosData != null) {
        _qosSettings = QoSSettings.fromJson(qosData);
      }

      if (_statistics != null || _qosSettings != null) {
        _setState(TrafficState.loaded);
      }
    } catch (e) {
      debugPrint('[Traffic Provider] Load stored data error: $e');
    }
  }

  // Refresh data from device
  Future<void> refreshData() async {
    try {
      _setState(TrafficState.loading);
      _clearError();

      // Get traffic statistics
      final statsData = await _apiService.getTrafficStatistics();
      if (statsData != null) {
        _statistics = TrafficStatistics.fromJson(statsData);
        await _storageService.saveAppSetting('traffic_statistics', _statistics!.toJson());
      }

      // Get QoS settings
      final qosData = await _apiService.getQoSSettings();
      if (qosData != null) {
        _qosSettings = QoSSettings.fromJson(qosData);
        await _storageService.saveAppSetting('qos_settings', _qosSettings!.toJson());
      }

      _setState(TrafficState.loaded);
    } catch (e) {
      debugPrint('[Traffic Provider] Refresh data error: $e');
      _setError('Failed to load traffic data');
      _setState(TrafficState.error);
    }
  }

  // Update QoS settings
  Future<bool> updateQoSSettings(QoSSettings settings) async {
    try {
      _setState(TrafficState.loading);
      _clearError();

      final success = await _apiService.setQoSSettings(
        enabled: settings.enabled,
        mode: settings.mode,
        settings: {
          'HighPriority': settings.highPriority.toString(),
          'MediumPriority': settings.mediumPriority.toString(),
          'LowPriority': settings.lowPriority.toString(),
          'DevicePriorities': settings.devicePriorities,
        },
      );

      if (success) {
        _qosSettings = settings;
        await _storageService.saveAppSetting('qos_settings', _qosSettings!.toJson());
        _setState(TrafficState.loaded);
        return true;
      } else {
        _setError('Failed to update QoS settings');
        _setState(TrafficState.error);
        return false;
      }
    } catch (e) {
      debugPrint('[Traffic Provider] Update QoS error: $e');
      _setError('Failed to update QoS settings');
      _setState(TrafficState.error);
      return false;
    }
  }

  // Set speed limit for specific device
  Future<bool> setDeviceSpeedLimit({
    required String deviceMac,
    required int uploadLimit,
    required int downloadLimit,
  }) async {
    try {
      final success = await _apiService.setSpeedLimit(
        deviceMac: deviceMac,
        uploadLimit: uploadLimit,
        downloadLimit: downloadLimit,
      );

      if (success) {
        // Refresh data to get updated statistics
        await refreshData();
        return true;
      } else {
        _setError('Failed to set speed limit');
        return false;
      }
    } catch (e) {
      debugPrint('[Traffic Provider] Set speed limit error: $e');
      _setError('Failed to set speed limit');
      return false;
    }
  }

  // Toggle QoS
  Future<bool> toggleQoS(bool enabled) async {
    if (_qosSettings == null) return false;

    final updatedSettings = _qosSettings!.copyWith(enabled: enabled);
    return await updateQoSSettings(updatedSettings);
  }

  // Set QoS mode
  Future<bool> setQoSMode(String mode) async {
    if (_qosSettings == null) return false;

    final updatedSettings = _qosSettings!.copyWith(mode: mode);
    return await updateQoSSettings(updatedSettings);
  }

  // Set device priority
  Future<bool> setDevicePriority(String deviceMac, int priority) async {
    if (_qosSettings == null) return false;

    final updatedPriorities = Map<String, int>.from(_qosSettings!.devicePriorities);
    updatedPriorities[deviceMac] = priority;

    final updatedSettings = _qosSettings!.copyWith(devicePriorities: updatedPriorities);
    return await updateQoSSettings(updatedSettings);
  }

  // Get current network usage percentage
  double getCurrentUsagePercentage(int limit) {
    if (_statistics == null || limit <= 0) return 0.0;
    return _statistics!.getTotalPercentage(limit);
  }

  // Check if approaching data limit
  bool isApproachingLimit(int limit, {double threshold = 0.8}) {
    if (_statistics == null) return false;
    return _statistics!.isApproachingLimit(limit, threshold: threshold);
  }

  // Get real-time speed
  String getCurrentSpeed() {
    if (_statistics == null) return '0 B/s';
    return '${_statistics!.formattedCurrentUploadSpeed} ↑ / ${_statistics!.formattedCurrentDownloadSpeed} ↓';
  }

  // Start auto-refresh timer
  void _startAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!isLoading) {
        refreshData();
      }
    });
  }

  // Stop auto-refresh timer
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  // Reset statistics (if supported by device)
  Future<bool> resetStatistics() async {
    try {
      // This would depend on the specific ZTE device API
      // Some devices support resetting traffic statistics
      final success = await _apiService.setQoSSettings(
        enabled: true,
        mode: 'reset',
        settings: {'Action': 'ResetStatistics'},
      );

      if (success) {
        await refreshData();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('[Traffic Provider] Reset statistics error: $e');
      return false;
    }
  }

  // Private helper methods
  void _setState(TrafficState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
}
