import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import '../widgets/custom_button.dart';
import '../providers/bandwidth_provider.dart';

class BandwidthSettingsScreen extends StatefulWidget {
  const BandwidthSettingsScreen({super.key});

  @override
  State<BandwidthSettingsScreen> createState() =>
      _BandwidthSettingsScreenState();
}

class _BandwidthSettingsScreenState extends State<BandwidthSettingsScreen> {
  final _uploadController = TextEditingController();
  final _downloadController = TextEditingController();
  bool _bandwidthDisplayEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  void _loadCurrentSettings() {
    // Load current bandwidth settings from provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<BandwidthProvider>(context, listen: false);
      provider.initialize();

      if (provider.settings != null) {
        _uploadController.text = provider.settings!.uploadSpeed.toString();
        _downloadController.text = provider.settings!.downloadSpeed.toString();
        _bandwidthDisplayEnabled = provider.settings!.isEnabled;
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _uploadController.dispose();
    _downloadController.dispose();
    super.dispose();
  }

  Future<void> _applySettings() async {
    final uploadSpeed = int.tryParse(_uploadController.text) ?? 0;
    final downloadSpeed = int.tryParse(_downloadController.text) ?? 0;

    if (uploadSpeed <= 0 || downloadSpeed <= 0) {
      _showErrorDialog('يرجى إدخال قيم صحيحة للسرعة');
      return;
    }

    // Apply bandwidth settings using provider
    try {
      final provider = Provider.of<BandwidthProvider>(context, listen: false);
      final success = await provider.updateSettings(
        uploadSpeed: uploadSpeed,
        downloadSpeed: downloadSpeed,
        isEnabled: _bandwidthDisplayEnabled,
      );

      if (mounted) {
        if (success) {
          _showSuccessDialog(AppStrings.bandwidthUpdateSuccess);
        } else {
          _showErrorDialog(
            provider.errorMessage ?? AppStrings.bandwidthUpdateFailed,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog(AppStrings.bandwidthUpdateFailed);
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('نجح'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.red,
        title: const Text(
          'إعدادات النطاق',
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.grey, Colors.white],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Bandwidth Display Toggle
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'حول عرض النطاق',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Switch(
                      value: _bandwidthDisplayEnabled,
                      onChanged: (value) {
                        setState(() {
                          _bandwidthDisplayEnabled = value;
                        });
                      },
                      activeColor: Colors.blue,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Upload Speed
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Text('• تحميل'),
                    const Spacer(),
                    SizedBox(
                      width: 100,
                      child: TextField(
                        controller: _uploadController,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text('kbits'),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Download Speed
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Text('• تحميل'),
                    const Spacer(),
                    SizedBox(
                      width: 100,
                      child: TextField(
                        controller: _downloadController,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text('kbits'),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Apply Button
              SizedBox(
                width: 120,
                child: ElevatedButton(
                  onPressed: _applySettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: const Text(
                    'تفعيل',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
