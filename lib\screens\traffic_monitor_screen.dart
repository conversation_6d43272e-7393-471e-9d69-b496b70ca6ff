import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';
import '../providers/traffic_provider.dart';
import '../models/traffic_statistics.dart';

class TrafficMonitorScreen extends StatefulWidget {
  const TrafficMonitorScreen({super.key});

  @override
  State<TrafficMonitorScreen> createState() => _TrafficMonitorScreenState();
}

class _TrafficMonitorScreenState extends State<TrafficMonitorScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<TrafficProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقب الترافيك'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<TrafficProvider>(context, listen: false).refreshData();
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Consumer<TrafficProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading && provider.statistics == null) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (provider.statistics == null) {
              return const Center(
                child: Text(
                  'لا توجد بيانات متاحة',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textSecondary,
                  ),
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: provider.refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current Speed Card
                    _buildCurrentSpeedCard(provider.statistics!),
                    
                    const SizedBox(height: 16),
                    
                    // Usage Statistics
                    _buildUsageStatistics(provider.statistics!),
                    
                    const SizedBox(height: 16),
                    
                    // QoS Settings
                    if (provider.qosSettings != null)
                      _buildQoSSettings(provider.qosSettings!, provider),
                    
                    const SizedBox(height: 16),
                    
                    // Connection Info
                    _buildConnectionInfo(provider.statistics!),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCurrentSpeedCard(TrafficStatistics stats) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'السرعة الحالية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  const Icon(
                    Icons.upload,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    stats.formattedCurrentUploadSpeed,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const Text(
                    'رفع',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
              Column(
                children: [
                  const Icon(
                    Icons.download,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    stats.formattedCurrentDownloadSpeed,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const Text(
                    'تحميل',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUsageStatistics(TrafficStatistics stats) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات الاستخدام',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatRow('إجمالي البيانات', stats.formattedTotalData),
          _buildStatRow('البيانات الشهرية', stats.formattedMonthlyData),
          _buildStatRow('البيانات اليومية', stats.formattedDailyData),
          _buildStatRow('الأجهزة المتصلة', '${stats.connectedDevices}'),
          _buildStatRow('نوع الاتصال', stats.connectionType),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQoSSettings(QoSSettings qos, TrafficProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'إعدادات جودة الخدمة (QoS)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Switch(
                value: qos.enabled,
                onChanged: (value) {
                  provider.toggleQoS(value);
                },
                activeColor: AppColors.primary,
              ),
            ],
          ),
          if (qos.enabled) ...[
            const SizedBox(height: 16),
            _buildStatRow('الوضع', qos.mode == 'auto' ? 'تلقائي' : 'يدوي'),
            _buildStatRow('أولوية عالية', '${qos.highPriority}%'),
            _buildStatRow('أولوية متوسطة', '${qos.mediumPriority}%'),
            _buildStatRow('أولوية منخفضة', '${qos.lowPriority}%'),
          ],
        ],
      ),
    );
  }

  Widget _buildConnectionInfo(TrafficStatistics stats) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'معلومات الاتصال',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatRow('آخر تحديث', _formatLastUpdate(stats.lastUpdated)),
          _buildStatRow('حالة الاتصال', 'متصل'),
          _buildStatRow('مدة الاتصال', _formatUptime(stats.lastUpdated)),
        ],
      ),
    );
  }

  String _formatLastUpdate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  String _formatUptime(DateTime startTime) {
    final now = DateTime.now();
    final uptime = now.difference(startTime);
    
    if (uptime.inDays > 0) {
      return '${uptime.inDays} يوم ${uptime.inHours % 24} ساعة';
    } else if (uptime.inHours > 0) {
      return '${uptime.inHours} ساعة ${uptime.inMinutes % 60} دقيقة';
    } else {
      return '${uptime.inMinutes} دقيقة';
    }
  }
}
