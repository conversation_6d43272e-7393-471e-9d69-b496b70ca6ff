import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';

import '../constants/app_constants.dart';
import '../models/connected_device.dart';
import '../models/device_info.dart';
import '../models/wifi_settings.dart';

class ApiService {
  factory ApiService() => _instance;
  ApiService._internal();
  static final ApiService _instance = ApiService._internal();

  late Dio _dio;
  String? _sessionToken;
  String? _tokenId;
  String _baseUrl = AppConstants.baseUrl;

  void initialize({String? baseUrl}) {
    _baseUrl = baseUrl ?? AppConstants.baseUrl;
    _dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        connectTimeout: const Duration(
          milliseconds: AppConstants.connectionTimeout,
        ),
        receiveTimeout: const Duration(
          milliseconds: AppConstants.receiveTimeout,
        ),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Referer': _baseUrl,
        },
      ),
    );

    // Add interceptors for logging and error handling
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('[API] $obj'),
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add session token to headers if available
          if (_sessionToken != null && _tokenId != null) {
            options.headers['__RequestVerificationToken'] = _sessionToken;
            options.headers['Cookie'] = 'SessionID=$_tokenId';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          print('[API Error] ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  // Authentication
  Future<bool> login(String username, String password) async {
    try {
      // First, get session token
      final tokenResponse = await _dio.get('/api/webserver/SesTokInfo');
      final tokenData = _parseXmlResponse(tokenResponse.data);

      _sessionToken = tokenData['SesInfo'];
      _tokenId = tokenData['TokInfo'];

      if (_sessionToken == null || _tokenId == null) {
        throw Exception('Failed to get session token');
      }

      // Create password hash
      final passwordHash = _createPasswordHash(password, _sessionToken!);

      // Login request
      final loginData = {
        'Username': username,
        'Password': passwordHash,
        'password_type': '4',
      };

      final response = await _dio.post(
        '/api/user/login',
        data: _buildXmlRequest(loginData),
        options: Options(
          headers: {
            '__RequestVerificationToken': _sessionToken,
            'Cookie': 'SessionID=$_tokenId',
          },
        ),
      );

      final responseData = _parseXmlResponse(response.data);
      return responseData['response'] == 'OK';
    } catch (e) {
      print('[Login Error] $e');
      return false;
    }
  }

  Future<bool> logout() async {
    try {
      await _dio.post('/api/user/logout');
      _sessionToken = null;
      _tokenId = null;
      return true;
    } catch (e) {
      print('[Logout Error] $e');
      return false;
    }
  }

  // Device Information
  Future<DeviceInfo?> getDeviceInfo() async {
    try {
      final response = await _dio.get('/api/device/information');
      final data = _parseXmlResponse(response.data);
      return DeviceInfo.fromJson(data);
    } catch (e) {
      print('[Device Info Error] $e');
      return null;
    }
  }

  // WiFi Settings
  Future<WifiSettings?> getWifiSettings() async {
    try {
      final response = await _dio.get('/api/wlan/basic-settings');
      final data = _parseXmlResponse(response.data);
      return WifiSettings.fromJson(data);
    } catch (e) {
      print('[WiFi Settings Error] $e');
      return null;
    }
  }

  Future<bool> updateWifiSettings(WifiSettings settings) async {
    try {
      final response = await _dio.post(
        '/api/wlan/basic-settings',
        data: _buildXmlRequest(settings.toJson()),
      );
      final data = _parseXmlResponse(response.data);
      return data['response'] == 'OK';
    } catch (e) {
      print('[Update WiFi Error] $e');
      return false;
    }
  }

  // Connected Devices
  Future<List<ConnectedDevice>> getConnectedDevices() async {
    try {
      final response = await _dio.get('/api/wlan/host-list');
      final data = _parseXmlResponse(response.data);

      final List<ConnectedDevice> devices = [];
      final hosts = data['Hosts'];

      if (hosts is List) {
        for (final host in hosts) {
          if (host is Map<String, dynamic>) {
            devices.add(ConnectedDevice.fromJson(host));
          }
        }
      } else if (hosts is Map<String, dynamic>) {
        devices.add(ConnectedDevice.fromJson(hosts));
      }

      return devices;
    } catch (e) {
      print('[Connected Devices Error] $e');
      return [];
    }
  }

  Future<bool> blockDevice(String macAddress) async {
    try {
      final data = {'MacAddress': macAddress, 'IsBlocked': '1'};

      final response = await _dio.post(
        '/api/wlan/multi-macfilter-settings',
        data: _buildXmlRequest(data),
      );

      final responseData = _parseXmlResponse(response.data);
      return responseData['response'] == 'OK';
    } catch (e) {
      print('[Block Device Error] $e');
      return false;
    }
  }

  Future<bool> unblockDevice(String macAddress) async {
    try {
      final data = {'MacAddress': macAddress, 'IsBlocked': '0'};

      final response = await _dio.post(
        '/api/wlan/multi-macfilter-settings',
        data: _buildXmlRequest(data),
      );

      final responseData = _parseXmlResponse(response.data);
      return responseData['response'] == 'OK';
    } catch (e) {
      print('[Unblock Device Error] $e');
      return false;
    }
  }

  // Helper methods
  String _createPasswordHash(String password, String token) {
    final combined = password + token;
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Map<String, dynamic> _parseXmlResponse(String xmlData) {
    // Simple XML parsing - in a real app, use a proper XML parser
    final Map<String, dynamic> result = {};

    // This is a simplified parser - you should use xml package for production
    final RegExp tagRegex = RegExp(r'<(\w+)>(.*?)</\1>');
    final matches = tagRegex.allMatches(xmlData);

    for (final match in matches) {
      final key = match.group(1)!;
      final value = match.group(2)!;
      result[key] = value;
    }

    return result;
  }

  String _buildXmlRequest(Map<String, dynamic> data) {
    final buffer = StringBuffer();
    buffer.write('<?xml version="1.0" encoding="UTF-8"?>');
    buffer.write('<request>');

    data.forEach((key, value) {
      buffer.write('<$key>$value</$key>');
    });

    buffer.write('</request>');
    return buffer.toString();
  }

  // Getters
  bool get isLoggedIn => _sessionToken != null && _tokenId != null;
  String get baseUrl => _baseUrl;

  // Bandwidth and QoS Management
  Future<Map<String, dynamic>?> getBandwidthSettings() async {
    try {
      final response = await _dio.get(AppConstants.bandwidthSettingsEndpoint);
      final data = _parseXmlResponse(response.data);
      return data;
    } catch (e) {
      print('[Bandwidth Settings Error] $e');
      return null;
    }
  }

  Future<bool> setBandwidthSettings({
    required int uploadSpeed,
    required int downloadSpeed,
    required bool enabled,
  }) async {
    try {
      final data = {
        'UploadSpeed': uploadSpeed.toString(),
        'DownloadSpeed': downloadSpeed.toString(),
        'BandwidthEnabled': enabled ? '1' : '0',
        'Unit': 'kbps',
      };

      final response = await _dio.post(
        AppConstants.bandwidthSettingsEndpoint,
        data: _buildXmlRequest(data),
      );

      final responseData = _parseXmlResponse(response.data);
      return responseData['response'] == 'OK';
    } catch (e) {
      print('[Set Bandwidth Error] $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getTrafficStatistics() async {
    try {
      final response = await _dio.get(AppConstants.trafficMonitoringEndpoint);
      final data = _parseXmlResponse(response.data);
      return data;
    } catch (e) {
      print('[Traffic Statistics Error] $e');
      return null;
    }
  }

  Future<bool> setSpeedLimit({
    required String deviceMac,
    required int uploadLimit,
    required int downloadLimit,
  }) async {
    try {
      final data = {
        'MacAddress': deviceMac,
        'UploadLimit': uploadLimit.toString(),
        'DownloadLimit': downloadLimit.toString(),
        'Enabled': '1',
      };

      final response = await _dio.post(
        AppConstants.speedLimitEndpoint,
        data: _buildXmlRequest(data),
      );

      final responseData = _parseXmlResponse(response.data);
      return responseData['response'] == 'OK';
    } catch (e) {
      print('[Set Speed Limit Error] $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getQoSSettings() async {
    try {
      final response = await _dio.get(AppConstants.qosSettingsEndpoint);
      final data = _parseXmlResponse(response.data);
      return data;
    } catch (e) {
      print('[QoS Settings Error] $e');
      return null;
    }
  }

  Future<bool> setQoSSettings({
    required bool enabled,
    required String mode, // 'auto', 'manual'
    required Map<String, dynamic> settings,
  }) async {
    try {
      final data = {
        'QoSEnabled': enabled ? '1' : '0',
        'QoSMode': mode,
        ...settings,
      };

      final response = await _dio.post(
        AppConstants.qosSettingsEndpoint,
        data: _buildXmlRequest(data),
      );

      final responseData = _parseXmlResponse(response.data);
      return responseData['response'] == 'OK';
    } catch (e) {
      print('[Set QoS Error] $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getNetworkStatistics() async {
    try {
      final response = await _dio.get(AppConstants.networkStatsEndpoint);
      final data = _parseXmlResponse(response.data);
      return data;
    } catch (e) {
      print('[Network Statistics Error] $e');
      return null;
    }
  }

  // Update base URL
  void updateBaseUrl(String newBaseUrl) {
    _baseUrl = newBaseUrl;
    _dio.options.baseUrl = newBaseUrl;
  }
}
