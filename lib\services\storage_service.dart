import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/device_info.dart';
import '../models/wifi_settings.dart';

class StorageService {
  factory StorageService() => _instance;
  StorageService._internal();
  static final StorageService _instance = StorageService._internal();

  SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call initialize() first.');
    }
    return _prefs!;
  }

  // Authentication Storage
  Future<bool> saveLoginCredentials({
    required String username,
    required String password,
    required String deviceIp,
    required bool rememberLogin,
  }) async {
    try {
      await Future.wait([
        prefs.setString(AppConstants.keyUsername, username),
        prefs.setString(AppConstants.keyPassword, password),
        prefs.setString(AppConstants.keyDeviceIp, deviceIp),
        prefs.setBool(AppConstants.keyRememberLogin, rememberLogin),
        prefs.setBool(AppConstants.keyIsLoggedIn, true),
      ]);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to save login credentials: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getLoginCredentials() async {
    try {
      final rememberLogin = prefs.getBool(AppConstants.keyRememberLogin) ?? false;
      if (!rememberLogin) return null;

      final username = prefs.getString(AppConstants.keyUsername);
      final password = prefs.getString(AppConstants.keyPassword);
      final deviceIp = prefs.getString(AppConstants.keyDeviceIp);

      if (username != null && password != null && deviceIp != null) {
        return {
          'username': username,
          'password': password,
          'deviceIp': deviceIp,
          'rememberLogin': rememberLogin,
        };
      }
    } catch (e) {
      print('[Storage Error] Failed to get login credentials: $e');
    }
    return null;
  }

  Future<bool> clearLoginCredentials() async {
    try {
      await Future.wait([
        prefs.remove(AppConstants.keyUsername),
        prefs.remove(AppConstants.keyPassword),
        prefs.remove(AppConstants.keyDeviceIp),
        prefs.remove(AppConstants.keySessionToken),
        prefs.setBool(AppConstants.keyIsLoggedIn, false),
      ]);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to clear login credentials: $e');
      return false;
    }
  }

  // Session Management
  Future<bool> saveSessionToken(String token) async {
    try {
      await prefs.setString(AppConstants.keySessionToken, token);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to save session token: $e');
      return false;
    }
  }

  String? getSessionToken() {
    try {
      return prefs.getString(AppConstants.keySessionToken);
    } catch (e) {
      print('[Storage Error] Failed to get session token: $e');
      return null;
    }
  }

  // Login Status
  bool isLoggedIn() {
    try {
      return prefs.getBool(AppConstants.keyIsLoggedIn) ?? false;
    } catch (e) {
      print('[Storage Error] Failed to get login status: $e');
      return false;
    }
  }

  Future<bool> setLoggedIn(bool isLoggedIn) async {
    try {
      await prefs.setBool(AppConstants.keyIsLoggedIn, isLoggedIn);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to set login status: $e');
      return false;
    }
  }

  // Device Information Storage
  Future<bool> saveDeviceInfo(DeviceInfo deviceInfo) async {
    try {
      final jsonString = jsonEncode(deviceInfo.toJson());
      await prefs.setString('device_info', jsonString);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to save device info: $e');
      return false;
    }
  }

  DeviceInfo? getDeviceInfo() {
    try {
      final jsonString = prefs.getString('device_info');
      if (jsonString != null) {
        final jsonData = jsonDecode(jsonString);
        return DeviceInfo.fromJson(jsonData);
      }
    } catch (e) {
      print('[Storage Error] Failed to get device info: $e');
    }
    return null;
  }

  // WiFi Settings Storage
  Future<bool> saveWifiSettings(WifiSettings wifiSettings) async {
    try {
      final jsonString = jsonEncode(wifiSettings.toJson());
      await prefs.setString('wifi_settings', jsonString);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to save WiFi settings: $e');
      return false;
    }
  }

  WifiSettings? getWifiSettings() {
    try {
      final jsonString = prefs.getString('wifi_settings');
      if (jsonString != null) {
        final jsonData = jsonDecode(jsonString);
        return WifiSettings.fromJson(jsonData);
      }
    } catch (e) {
      print('[Storage Error] Failed to get WiFi settings: $e');
    }
    return null;
  }

  // App Settings
  Future<bool> saveAppSetting(String key, dynamic value) async {
    try {
      if (value is String) {
        await prefs.setString(key, value);
      } else if (value is int) {
        await prefs.setInt(key, value);
      } else if (value is double) {
        await prefs.setDouble(key, value);
      } else if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is List<String>) {
        await prefs.setStringList(key, value);
      } else {
        // Convert to JSON string for complex objects
        await prefs.setString(key, jsonEncode(value));
      }
      return true;
    } catch (e) {
      print('[Storage Error] Failed to save app setting $key: $e');
      return false;
    }
  }

  T? getAppSetting<T>(String key, {T? defaultValue}) {
    try {
      if (T == String) {
        return prefs.getString(key) as T? ?? defaultValue;
      } else if (T == int) {
        return prefs.getInt(key) as T? ?? defaultValue;
      } else if (T == double) {
        return prefs.getDouble(key) as T? ?? defaultValue;
      } else if (T == bool) {
        return prefs.getBool(key) as T? ?? defaultValue;
      } else if (T == List<String>) {
        return prefs.getStringList(key) as T? ?? defaultValue;
      } else {
        // Try to decode from JSON string
        final jsonString = prefs.getString(key);
        if (jsonString != null) {
          return jsonDecode(jsonString) as T;
        }
      }
    } catch (e) {
      print('[Storage Error] Failed to get app setting $key: $e');
    }
    return defaultValue;
  }

  // Recent Device IPs
  Future<bool> addRecentDeviceIP(String ip) async {
    try {
      final recentIPs = getRecentDeviceIPs();
      recentIPs.remove(ip); // Remove if already exists
      recentIPs.insert(0, ip); // Add to beginning
      
      // Keep only last 5 IPs
      if (recentIPs.length > 5) {
        recentIPs.removeRange(5, recentIPs.length);
      }
      
      await prefs.setStringList('recent_device_ips', recentIPs);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to add recent device IP: $e');
      return false;
    }
  }

  List<String> getRecentDeviceIPs() {
    try {
      return prefs.getStringList('recent_device_ips') ?? [];
    } catch (e) {
      print('[Storage Error] Failed to get recent device IPs: $e');
      return [];
    }
  }

  // Clear all data
  Future<bool> clearAllData() async {
    try {
      await prefs.clear();
      return true;
    } catch (e) {
      print('[Storage Error] Failed to clear all data: $e');
      return false;
    }
  }

  // Check if key exists
  bool hasKey(String key) {
    try {
      return prefs.containsKey(key);
    } catch (e) {
      print('[Storage Error] Failed to check key existence: $e');
      return false;
    }
  }

  // Remove specific key
  Future<bool> removeKey(String key) async {
    try {
      await prefs.remove(key);
      return true;
    } catch (e) {
      print('[Storage Error] Failed to remove key $key: $e');
      return false;
    }
  }
}
